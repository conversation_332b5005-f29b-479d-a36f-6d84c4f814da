import Foundation
import UIKit
import Vision
import CoreImage

/// Service voor het verwerken van afbeeldingen, inclusief het maken van cut-out images
class ImageProcessingService {
    static let shared = ImageProcessingService()
    
    private init() {}
    
    /// Genereert een cut-out image van een persoon uit een foto
    /// - Parameter image: De originele foto
    /// - Returns: Een cut-out image met transparante achtergrond, of nil als het niet lukt
    func generateCutoutImage(from image: UIImage) async -> UIImage? {
        guard let normalizedImage = normalizeImageOrientation(image) else { return nil }
        guard let inputImage = CIImage(image: normalizedImage) else { return nil }
        
        return await withCheckedContinuation { continuation in
            let request = VNGeneratePersonSegmentationRequest { request, error in
                guard let observations = request.results as? [VNPixelBufferObservation],
                      let observation = observations.first else {
                    continuation.resume(returning: nil)
                    return
                }
                
                let maskImage = CIImage(cvPixelBuffer: observation.pixelBuffer)
                
                // Scale mask to match input image size
                let scaleX = inputImage.extent.width / maskImage.extent.width
                let scaleY = inputImage.extent.height / maskImage.extent.height
                let scaledMask = maskImage.transformed(by: CGAffineTransform(scaleX: scaleX, y: scaleY))
                
                // Apply mask to create cut-out
                let filter = CIFilter.blendWithMask()
                filter.inputImage = inputImage
                filter.backgroundImage = CIImage.empty()
                filter.maskImage = scaledMask
                
                guard let maskedImage = filter.outputImage else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // Find the bounding box of the non-transparent pixels
                let context = CIContext()
                guard let cgMaskedImage = context.createCGImage(maskedImage, from: maskedImage.extent) else {
                    continuation.resume(returning: nil)
                    return
                }
                
                // Create UIImage from the masked result
                let maskedUIImage = UIImage(cgImage: cgMaskedImage)
                
                // Crop to the person's bounding box
                if let croppedImage = self.cropToContent(maskedUIImage) {
                    continuation.resume(returning: croppedImage)
                } else {
                    continuation.resume(returning: maskedUIImage)
                }
            }
            
            request.qualityLevel = .balanced
            request.outputPixelFormat = kCVPixelFormatType_OneComponent8
            
            guard let cgImage = normalizedImage.cgImage else {
                continuation.resume(returning: nil)
                return
            }
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                continuation.resume(returning: nil)
            }
        }
    }
    
    /// Converteert een cut-out image naar Data voor opslag
    /// - Parameter image: De cut-out image
    /// - Returns: PNG data van de image
    func cutoutImageToData(_ image: UIImage) -> Data? {
        return image.pngData()
    }
    
    /// Converteert Data terug naar een UIImage
    /// - Parameter data: PNG data
    /// - Returns: UIImage of nil
    func dataToImage(_ data: Data) -> UIImage? {
        return UIImage(data: data)
    }
    
    // MARK: - Private Helper Methods
    
    private func cropToContent(_ image: UIImage) -> UIImage? {
        guard let cgImage = image.cgImage else { return nil }

        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        guard let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else { return nil }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = context.data else { return nil }
        let buffer = data.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)

        var minX = width, maxX = 0, minY = height, maxY = 0

        // Find bounding box of non-transparent pixels
        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel
                let alpha = buffer[pixelIndex + 3]

                if alpha > 0 {
                    minX = min(minX, x)
                    maxX = max(maxX, x)
                    minY = min(minY, y)
                    maxY = max(maxY, y)
                }
            }
        }

        // Add padding
        let padding = 10
        minX = max(0, minX - padding)
        maxX = min(width - 1, maxX + padding)
        minY = max(0, minY - padding)
        maxY = min(height - 1, maxY + padding)

        // Make the crop more rectangular/elongated for better vertical fit
        let originalWidth = maxX - minX + 1
        let originalHeight = maxY - minY + 1

        // Calculate target aspect ratio (more vertical/elongated)
        let targetAspectRatio: CGFloat = 0.625 // Width/Height ratio (1/1.6 = zelfde breedte, 60% langer)
        let currentAspectRatio = CGFloat(originalWidth) / CGFloat(originalHeight)

        var finalMinX = minX
        var finalMaxX = maxX
        var finalMinY = minY
        var finalMaxY = maxY

        if currentAspectRatio > targetAspectRatio {
            // Current crop is too wide, make it taller
            let targetWidth = Int(CGFloat(originalHeight) * targetAspectRatio)
            let widthReduction = (originalWidth - targetWidth) / 2
            finalMinX = max(0, minX + widthReduction)
            finalMaxX = min(width - 1, maxX - widthReduction)
        } else {
            // Current crop is too tall, make it wider (less likely for person silhouettes)
            let targetHeight = Int(CGFloat(originalWidth) / targetAspectRatio)
            let heightIncrease = (targetHeight - originalHeight) / 2
            finalMinY = max(0, minY - heightIncrease)
            finalMaxY = min(height - 1, maxY + heightIncrease)
        }

        let cropRect = CGRect(x: finalMinX, y: finalMinY, width: finalMaxX - finalMinX + 1, height: finalMaxY - finalMinY + 1)

        guard let croppedCGImage = cgImage.cropping(to: cropRect) else { return nil }

        return UIImage(cgImage: croppedCGImage)
    }
    
    private func normalizeImageOrientation(_ image: UIImage) -> UIImage? {
        if image.imageOrientation == .up {
            return image
        }
        
        let size = image.size
        UIGraphicsBeginImageContextWithOptions(size, false, image.scale)
        defer { UIGraphicsEndImageContext() }
        
        image.draw(in: CGRect(origin: .zero, size: size))
        
        guard let normalizedImage = UIGraphicsGetImageFromCurrentImageContext() else {
            return nil
        }
        
        guard let cgImage = normalizedImage.cgImage else { return nil }
        return UIImage(cgImage: cgImage, scale: image.scale, orientation: .up)
    }
}
