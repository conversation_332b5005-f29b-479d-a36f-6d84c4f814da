import SwiftUI

struct CutoutCropView: View {
    let image: UIImage
    let onComplete: (UIImage?) -> Void

    @State private var offsetLimit: CGSize = .zero
    @State private var offset = CGSize.zero
    @State private var lastOffset: CGSize = .zero
    @State private var scale: CGFloat = 1
    @State private var lastScale: CGFloat = 0
    @State private var imageViewSize: CGSize = .zero
    @State private var croppedImage: UIImage?

    // Rechthoekige mask voor cut-out (aspect ratio 0.83 = 1/1.2)
    private let mask = CGSize(width: 200, height: 240)
    
    var body: some View {
        let dragGesture = DragGesture()
            .onChanged { gesture in
                offsetLimit = getOffsetLimit()

                let width = min(
                    max(-offsetLimit.width, lastOffset.width + gesture.translation.width),
                    offsetLimit.width
                )
                let height = min(
                    max(-offsetLimit.height, lastOffset.height + gesture.translation.height),
                    offsetLimit.height
                )

                offset = CGSize(width: width, height: height)
            }
            .onEnded { value in
                lastOffset = offset
            }

        let scaleGesture = MagnificationGesture()
            .onChanged { gesture in
                let scaledValue = (gesture - 1) * 0.5 + 1
                scale = min(max(scaledValue * lastScale, max(mask.width / imageViewSize.width, mask.height / imageViewSize.height)), 5)
            }
            .onEnded { _ in
                lastScale = scale
                lastOffset = offset
            }

        NavigationView {
            ZStack(alignment: .center) {
                ZStack {
                    Rectangle()
                        .fill(.ultraThickMaterial)
                        .ignoresSafeArea()
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFit()
                        .scaleEffect(scale)
                        .offset(offset)
                }
                .blur(radius: 20)

                Image(uiImage: image)
                    .resizable()
                    .scaledToFit()
                    .scaleEffect(scale)
                    .offset(offset)
                    .mask(
                        Rectangle()
                            .frame(width: mask.width, height: mask.height)
                    )
                    .overlay {
                        // Crop frame voor cut-out
                        Rectangle()
                            .stroke(Color.yellow, lineWidth: 2)
                            .frame(width: mask.width, height: mask.height)
                        
                        // Label
                        Text("Cut-out Crop")
                            .font(.caption)
                            .foregroundColor(.yellow)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.black.opacity(0.6))
                            .cornerRadius(4)
                            .offset(y: -mask.height/2 - 20)
                    }
            }
            .overlay(
                // Invisible overlay to capture all gestures and prevent sheet dismissal
                Rectangle()
                    .fill(Color.clear)
                    .contentShape(Rectangle())
                    .gesture(dragGesture)
                    .gesture(scaleGesture)
            )
            .interactiveDismissDisabled(true)
            .presentationDragIndicator(.hidden)
            .navigationTitle("Cut-out Crop")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        onComplete(nil)
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        croppedImage = cropImage(
                            image,
                            toRect: CGRect(
                                x: (((imageViewSize.width) - (mask.width / scale)) / 2 - offset.width / scale),
                                y: (((imageViewSize.height) - (mask.height / scale)) / 2 - offset.height / scale),
                                width: mask.width / scale,
                                height: mask.height / scale
                            ),
                            viewWidth: UIScreen.main.bounds.width,
                            viewHeight: UIScreen.main.bounds.height
                        )
                    }
                    .fontWeight(.semibold)
                }
            }
            .onChange(of: croppedImage) { _, newValue in
                onComplete(newValue)
            }
            .onAppear {
                let factor = UIScreen.main.bounds.width / image.size.width
                imageViewSize.height = image.size.height * factor
                imageViewSize.width = image.size.width * factor
            }
        }
    }
    
    func getOffsetLimit() -> CGSize {
        var offsetLimit: CGSize = .zero
        offsetLimit.width = ((imageViewSize.width * scale) - mask.width) / 2
        offsetLimit.height = ((imageViewSize.height * scale) - mask.height) / 2
        return offsetLimit
    }

    func cropImage(_ inputImage: UIImage, toRect cropRect: CGRect, viewWidth: CGFloat, viewHeight: CGFloat) -> UIImage? {
        let imageViewScale = max(inputImage.size.width / viewWidth,
                                inputImage.size.height / viewHeight)

        var cropZone: CGRect

        if inputImage.imageOrientation == .right {
            cropZone = CGRect(x: cropRect.origin.y * imageViewScale,
                              y: inputImage.size.width - (cropRect.size.width * imageViewScale) - (cropRect.origin.x * imageViewScale),
                              width: cropRect.size.height * imageViewScale,
                              height: cropRect.size.width * imageViewScale)
        } else if inputImage.imageOrientation == .down {
            cropZone = CGRect(x: inputImage.size.width - (cropRect.origin.x * imageViewScale),
                              y: inputImage.size.height - (cropRect.origin.y * imageViewScale),
                              width: -cropRect.size.width * imageViewScale,
                              height: -cropRect.size.height * imageViewScale)
        } else {
            cropZone = CGRect(x: cropRect.origin.x * imageViewScale,
                              y: cropRect.origin.y * imageViewScale,
                              width: cropRect.size.width * imageViewScale,
                              height: cropRect.size.height * imageViewScale)
        }

        // Perform cropping in Core Graphics
        guard let cutImageRef: CGImage = inputImage.cgImage?.cropping(to: cropZone) else {
            return nil
        }

        // Return image to UIImage
        let croppedImage: UIImage = UIImage(cgImage: cutImageRef)

        return croppedImage.fixOrientation(og: inputImage)
    }
}

#Preview {
    CutoutCropView(image: UIImage(systemName: "photo")!) { _ in
        // Preview completion handler
    }
}
